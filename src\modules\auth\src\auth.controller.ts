import { <PERSON>, Get, Req, Res, UnauthorizedException, <PERSON>gger, HttpStatus, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiCookieAuth, ApiParam } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthService } from './auth.service';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private readonly authService: AuthService) {}

  @Get('me')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get current user information',
    description: 'Authenticates user using encrypted cookies, decrypts access token, verifies JWT locally, and returns user information from JWT payload (no external API calls)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT payload returned successfully',
    schema: {
      type: 'object',
      properties: {
        iss: { type: 'string', description: 'Issuer' },
        sub: { type: 'string', description: 'Subject (user ID)' },
        email: { type: 'string', description: 'User email' },
        aud: { type: 'array', items: { type: 'string' }, description: 'Audience' },
        exp: { type: 'number', description: 'Expiration time' },
        iat: { type: 'number', description: 'Issued at time' },
        jti: { type: 'string', description: 'JWT ID' },
        sid: { type: 'string', description: 'Session ID' },
        azp: { type: 'string', description: 'Authorized party' },
        ent_set: { type: 'object', description: 'Entity set' },
        perm_v: { type: 'number', description: 'Permission version' },
        amr: { type: 'array', items: { type: 'string' }, description: 'Authentication methods references' },
        auth_time: { type: 'number', description: 'Authentication time' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing access token',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`🔐 [AUTH CONTROLLER] /auth/me endpoint called`);
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`🔑 [AUTH CONTROLLER] Access token present: ${!!cookies.access_token}`);

      // Authenticate user using the auth service - now returns JWT payload
      this.logger.log(`🔧 [AUTH CONTROLLER] About to call authenticateUserFromCookies method`);
      const jwtPayload = await this.authService.authenticateUserFromCookies(cookies);
      this.logger.log(`🔧 [AUTH CONTROLLER] Method returned, type: ${typeof jwtPayload}, keys: ${Object.keys(jwtPayload)}`);

      this.logger.log(`✅ [AUTH CONTROLLER] JWT payload authenticated successfully for user: ${jwtPayload.sub}`);

      return reply.status(HttpStatus.OK).send(jwtPayload);
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Get('users/:id')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by ID from external auth service using encrypted access token'
  })
  @ApiParam({
    name: 'id',
    description: 'User unique identifier (UUID)',
    type: 'string',
    format: 'uuid'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed or user not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Failed to retrieve user' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Getting user by ID: ${id}`);

      // Get user from external auth service
      const user = await this.authService.getUserById(id, cookies);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Get user by ID failed:', error);
      throw new UnauthorizedException((error instanceof Error ? error.message : String(error)) || 'Failed to retrieve user');
    }
  }

  @Get('users')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve all users from external auth service using encrypted access token'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Failed to retrieve users' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getAllUsers(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log('Getting all users');

      // Get users from external auth service
      const users = await this.authService.getAllUsers(cookies);

      return reply.status(HttpStatus.OK).send(users);
    } catch (error) {
      this.logger.error('Get all users failed:', error);
      throw new UnauthorizedException((error instanceof Error ? error.message : String(error)) || 'Failed to retrieve users');
    }
  }
}
