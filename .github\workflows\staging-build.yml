name: Deploy to Staging 

  

env: 

  CONTEXT_DIR: "./" 

  IMAGE_NAME: ${{ github.repository }}/develop 

  DOCKERFILE: Dockerfile 

  DOCKER_REGISTRY: docker.io 

  OWNER: blee900 

  DOCKER_REPOSITORY: product-backend   ## ie. Service name is account, partner, support, etc. Then a (dash)…. –backend, -frontend, -admin ex. account-frontend 

  

on: 

  push: 

    branches: 

      - staging 

    # paths-ignore: 

    #   - '.github/workflows/**' 

  workflow_dispatch: 

   

jobs: 

  build-and-publish: 

    runs-on: ubuntu-latest 

    steps: 

      - name: Checkout Code 

        uses: actions/checkout@v4 

  

      - name: Extract Docker metadata 

        id: meta 

        uses: docker/metadata-action@v5 

        with: 

          images: ${{ env.DOCKER_REGISTRY }}/${{ env.OWNER }}/${{ env.DOCKER_REPOSITORY }} 

          tags: |  

           type=sha,prefix=,format=short 

             

      - name: Log into Dockerhub 

        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 

        with: 

          registry: ${{ env.DOCKER_REGISTRY }} 

          username: ${{ secrets.DOCKER_USERNAME }} 

          password: ${{ secrets.DOCKER_PASSWORD }} 

       

      - name: Preset Image Name 

        run: echo "IMAGE_URL=$(echo ${{ steps.meta.outputs.tags }} | tr -d '[]' | cut -d' ' -f1)" >> $GITHUB_ENV 

      

      - name: Build and push Docker Image 

        uses: docker/build-push-action@v6 

        with: 

          context: . 

          file: ./Dockerfile 

          push: true 

          tags: ${{ steps.meta.outputs.tags }} 

          labels: ${{ steps.meta.outputs.labels }} 

       

      - name: Show Docker Image Name 

        run: | 

          IMAGE_URL=$(echo "${{ steps.meta.outputs.tags }}" | tr -d '[]' | cut -d' ' -f1) 

          echo "✅ Docker Image is ready to pull: $IMAGE_URL" 