import { Controller, Post, Get, Patch, Delete, Param, Body, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { VariantService } from './variant.service';
import { CreateVariantDto, UpdateVariantDto } from './dto/variant.dto';
import { Public } from '../modules/auth/src/auth.guard';

@ApiTags('Variants')
@Controller('products/:productId/variants')
export class VariantController {
  constructor(private readonly variantService: VariantService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new variant for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiBody({ type: CreateVariantDto })
  @ApiResponse({ status: 201, description: 'Variant created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data' })
  async create(
    @Param('productId') productId: string,
    @Body() dto: CreateVariantDto
  ) {
    // Ensure productId is set from the route
    return this.variantService.create({ ...dto, productId });
  }

  @Get()
  @ApiOperation({ summary: 'Get all variants for a product' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Variants retrieved successfully' })
  async findAll(@Param('productId') productId: string) {
    return this.variantService.findAllByProduct(productId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a variant by ID' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'id', description: 'Variant ID' })
  @ApiResponse({ status: 200, description: 'Variant retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Variant not found' })
  async findOne(
    @Param('productId') productId: string,
    @Param('id') id: string
  ) {
    return this.variantService.findOne(id, productId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a variant' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'id', description: 'Variant ID' })
  @ApiBody({ type: UpdateVariantDto })
  @ApiResponse({ status: 200, description: 'Variant updated successfully' })
  @ApiResponse({ status: 404, description: 'Variant not found' })
  async update(
    @Param('productId') productId: string,
    @Param('id') id: string,
    @Body() dto: UpdateVariantDto
  ) {
    return this.variantService.update(id, productId, dto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a variant' })
  @ApiParam({ name: 'productId', description: 'Product ID' })
  @ApiParam({ name: 'id', description: 'Variant ID' })
  @ApiResponse({ status: 200, description: 'Variant deleted successfully' })
  @ApiResponse({ status: 404, description: 'Variant not found' })
  async remove(
    @Param('productId') productId: string,
    @Param('id') id: string
  ) {
    return this.variantService.remove(id, productId);
  }
}