services:
  db:
    image: postgres:latest
    container_name: ngnair-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: pg-admin
      POSTGRES_PASSWORD: localpassword
      POSTGRES_DB: product-db
    volumes:
      - pg_data:/var/lib/postgresql/data
      - ./z-init.d:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pg-admin -d product-db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:alpine
    container_name: auth-redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  product-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ngnair-product-backend
    restart: unless-stopped
    ports:
      - "3070:3070"
    environment:
      # Database Configuration
      DATABASE_URL: *******************************************/product-db
      SHADOW_DATABASE_URL: *******************************************/product-shadowdb
      DATABASE_ENV: development

      # Redis Configuration
      REDIS_URL: redis://redis:6379

      # Server Configuration
      API_PORT: 3070
      API_PREFIX: /api/v1
      PORT: 3070
      NODE_ENV: development

      # Custom Domain Configuration
      SOURCE_IP: *************
      CORS_SUBDOMAIN: dev1.ngnair.com
      CORS_ORIGINS: ""

      # JWT Configuration
      JWT_SECRET: your-super-secret-jwt-key-here-product-microservice
      JWT_EXPIRES_IN: 24h

      # CORS Configuration
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001

      # Authentication Configuration
      AUTH_JWKS_URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      AUTH_SERVICE_URL: https://ng-auth-dev.dev1.ngnair.com
      ACCESS_TOKEN_ENCRYPTION_KEY: b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      COOKIE_SECRET: default-secret-key-for-development

      # Backblaze B2 configuration (if needed)
      B2_KEY_ID: 0057ca20a119bc10000000005
      B2_APP_KEY: K005KJ/zZeDrMCCGxGhLTKOum5YeY/Y
      B2_BUCKET_NAME: ngnair-product
      B2_BUCKET_ID: 270cdaf2b00a6111997b0c11
      B2_ENDPOINT: s3.us-east-005.backblazeb2.com

      # Logging configuration
      LOG: "true"

      # Health token
      HEALTH_TOKEN: ThisMustBeChanged
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3070/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    volumes:
      - ./logs:/home/<USER>/logs

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ngnair-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    depends_on:
      - db
    restart: unless-stopped
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  pg_data:
  pgadmin_data: