import { PartialType } from '@nestjs/mapped-types';
import { InputType, Field } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { CreateProductDto } from './create-product.dto';
import { SaleType } from '@prisma/client';
import { IsEnum, IsOptional, IsArray, IsString } from 'class-validator';

@InputType()
export class UpdateProductDto extends PartialType(CreateProductDto) {
	@Field(() => SaleType, { nullable: true })
	@ApiProperty({
		description: 'Type of sale for this product',
		enum: SaleType,
		example: SaleType.SUBSCRIPTION,
		required: false
	})
	@IsOptional()
	@IsEnum(SaleType)
	saleType?: SaleType;

	@Field(() => [String], { nullable: true })
	@ApiProperty({
		description: 'Updated array of product image URLs',
		example: [
			'https://example.com/images/product1-updated-front.jpg',
			'https://example.com/images/product1-updated-side.jpg'
		],
		type: [String],
		required: false
	})
	@IsOptional()
	@IsArray()
	@IsString({ each: true })
	productImages?: string[];
}