import { IsString, IsOptional, IsInt, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

// DTO for creating a variant
export class CreateVariantDto {
  @ApiProperty({
    description: 'Variant name (e.g., size, color, model)',
    example: 'Large - Black',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'Unique SKU for this variant',
    example: 'PWH-001-BLK-L',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  sku!: string;

  @ApiProperty({
    description: 'Price for this variant in cents (overrides product price if set)',
    example: 3499,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  price?: number;

  @ApiProperty({
    description: 'Variant-specific attributes (size, color, material, etc.)',
    example: {
      size: 'Large',
      color: 'Black',
      material: 'Premium Leather',
      weight: '280g'
    }
  })
  @IsOptional()
  @IsObject()
  attributes?: any;

  @ApiProperty({
    description: 'Product ID this variant belongs to',
    example: 'prod_12345',
    format: 'uuid'
  })
  @IsString()
  productId!: string;
}

// DTO for updating a variant
export class UpdateVariantDto {
  @ApiProperty({
    description: 'Updated variant name',
    example: 'Extra Large - Black',
    minLength: 1,
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Updated SKU for this variant',
    example: 'PWH-001-BLK-XL',
    minLength: 1,
    maxLength: 100,
    required: false
  })
  @IsOptional()
  @IsString()
  sku?: string;

  @ApiProperty({
    description: 'Updated price for this variant in cents',
    example: 3999,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  price?: number;

  @ApiProperty({
    description: 'Updated variant-specific attributes',
    example: {
      size: 'Extra Large',
      color: 'Black',
      material: 'Premium Leather',
      weight: '320g'
    }
  })
  @IsOptional()
  @IsObject()
  attributes?: any;
}