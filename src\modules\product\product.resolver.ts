import { Resolver, Query, Mutation, Args, ID, Int, ResolveField, Parent, Context } from '@nestjs/graphql';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';
import { ProductService } from './product.service';
import { ProductEntity } from './entities/product.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CategoryEntity } from '../category/entities/category.entity';
import { SubCategoryEntity } from '../subcategory/entities/subcategory.entity';
import { MerchantEntity } from '../merchant/entities/merchant.entity';
import { VariantEntity } from '../../variant/entities/variant.entity';
import { DiscountEntity } from '../discount/entities/discount.entity';
import { SubscriptionPlanEntity } from '../subscription-plan/entities/subscription-plan.entity';
import { PaymentPlanEntity } from '../payment-plan/entities/payment-plan.entity';

@Resolver(() => ProductEntity)
export class ProductResolver {
  constructor(private readonly productService: ProductService) {}

  @Query(() => [ProductEntity], { name: 'products' })
  async findAll(
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.productService.findAll(finalMerchantId);
  }

  @Query(() => ProductEntity, { name: 'product' })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @Args('merchantId', { nullable: true }) merchantId?: string,
    @Context() context?: any
  ) {
    // Use merchantId from context if authenticated, otherwise require it as parameter
    const finalMerchantId = context?.req?.user?.merchantId || merchantId;

    if (!finalMerchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.productService.findOne(id, finalMerchantId);
  }

  @Mutation(() => ProductEntity)
  async createProduct(
    @Args('createProductInput') createProductDto: CreateProductDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId || createProductDto.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    if (!createProductDto.categoryId) {
      throw new BadRequestException('categoryId is required');
    }

    // Ensure the product is created for the authenticated user's merchant
    const productData = { ...createProductDto, merchantId };
    return this.productService.create(productData);
  }

  @Mutation(() => ProductEntity)
  async updateProduct(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateProductInput') updateProductDto: UpdateProductDto,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    return this.productService.update(id, merchantId, updateProductDto);
  }

  @Mutation(() => Boolean)
  async removeProduct(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Require authentication for mutations
    if (!context?.req?.user) {
      throw new UnauthorizedException('Authentication required');
    }

    // Use merchantId from authenticated user
    const merchantId = context.req.user.merchantId;

    if (!merchantId) {
      throw new BadRequestException('merchantId is required');
    }
    await this.productService.remove(id, merchantId);
    return true;
  }

  // Field resolvers for relations
  @ResolveField(() => CategoryEntity, { nullable: true })
  async category(@Parent() product: ProductEntity) {
    // TODO: Implement category fetching when CategoryService is available
    return null;
  }

  @ResolveField(() => SubCategoryEntity, { nullable: true })
  async subCategory(@Parent() product: ProductEntity) {
    // TODO: Implement subcategory fetching when SubCategoryService is available
    return null;
  }

  @ResolveField(() => MerchantEntity)
  async merchant(@Parent() product: ProductEntity) {
    // TODO: Implement merchant fetching when MerchantService is available
    return null;
  }

  @ResolveField(() => [VariantEntity])
  async variants(@Parent() product: ProductEntity) {
    // TODO: Implement variant fetching when VariantService is available
    return [];
  }

  @ResolveField(() => [DiscountEntity])
  async discounts(@Parent() product: ProductEntity) {
    // TODO: Implement discount fetching when DiscountService is available
    return [];
  }

  @ResolveField(() => [SubscriptionPlanEntity])
  async subscriptionPlans(@Parent() product: ProductEntity) {
    // TODO: Implement subscription plan fetching when SubscriptionPlanService is available
    return [];
  }

  @ResolveField(() => [PaymentPlanEntity])
  async paymentPlans(@Parent() product: ProductEntity) {
    // TODO: Implement payment plan fetching when PaymentPlanService is available
    return [];
  }
}
