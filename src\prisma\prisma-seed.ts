import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Check if merchants already exist to avoid duplicate seeding
    const existingMerchants = await prisma.merchant.count();
    
    if (existingMerchants > 0) {
      console.log('✅ Database already seeded, skipping...');
      return;
    }

    // Create a default merchant
    const merchant = await prisma.merchant.create({
      data: {
        name: 'Default Merchant',
      },
    });

    console.log(`✅ Created merchant: ${merchant.name} (${merchant.id})`);

    // Create default categories
    const categories = await Promise.all([
      prisma.category.create({
        data: {
          name: 'Electronics',
          merchantId: merchant.id,
          subCategories: {
            create: [
              { name: 'Smartphones' },
              { name: 'Laptops' },
              { name: 'Accessories' },
            ],
          },
        },
      }),
      prisma.category.create({
        data: {
          name: 'Clothing',
          merchantId: merchant.id,
          subCategories: {
            create: [
              { name: 'Men' },
              { name: 'Women' },
              { name: 'Kids' },
            ],
          },
        },
      }),
    ]);

    console.log(`✅ Created ${categories.length} categories with subcategories`);

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
