import { ObjectType, Field, ID } from '@nestjs/graphql';
import { ProductEntity } from '../../product/entities/product.entity';
import { CategoryEntity } from '../../category/entities/category.entity';
import { DiscountEntity } from '../../discount/entities/discount.entity';

@ObjectType()
export class MerchantEntity {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  // Relations
  @Field(() => [ProductEntity])
  products: ProductEntity[];

  @Field(() => [CategoryEntity])
  categories: CategoryEntity[];

  @Field(() => [DiscountEntity])
  discounts: DiscountEntity[];
}
