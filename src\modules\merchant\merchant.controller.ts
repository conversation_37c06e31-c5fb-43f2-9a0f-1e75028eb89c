import { Controller, Get, Query, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger';
import { MerchantService } from './merchant.service';
import { CreateMerchantDto } from './dto/merchant.dto';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Merchants')
@Controller('merchants')
export class MerchantController {
  constructor(private readonly merchantService: MerchantService) {}

  // GET /merchants/exists?merchantId=xxx
  @Get('exists')
  @ApiOperation({ summary: 'Check if merchant exists' })
  @ApiQuery({ name: 'merchantId', required: true, description: 'Merchant ID to check' })
  @ApiResponse({ status: 200, description: 'Merchant existence check result', schema: {
    type: 'object',
    properties: {
      exists: { type: 'boolean', example: true }
    }
  }})
  async exists(@Query('merchantId') merchantId: string) {
    const exists = await this.merchantService.exists(merchantId);
    return { exists };
  }

  @Post()
  @ApiOperation({ summary: 'Create a new merchant' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: 'test-merchant' },
        name: { type: 'string', example: 'Test Merchant Store' }
      },
      required: ['id', 'name']
    }
  })
  @ApiResponse({ status: 201, description: 'Merchant created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - invalid data' })
  async create(@Body() dto: CreateMerchantDto) {
    return this.merchantService.create(dto);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all merchants' })
  @ApiResponse({ status: 200, description: 'Merchants retrieved successfully' })
  async findAll() {
    return this.merchantService.findAll();
  }
}
