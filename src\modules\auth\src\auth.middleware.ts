import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AuthService } from './auth.service';
import { User } from './types/auth.types';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AuthMiddleware.name);

  constructor(private authService: AuthService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Skip authentication for public routes
      const path = req.url || req.path || '';
      if (this.isPublicRoute(path)) {
        return next();
      }

      // Extract cookies
      const cookies = req.cookies || {};

      // Try to authenticate user
      try {
        const user = await this.authService.authenticateFromCookies(cookies);
        req.user = user;
        this.logger.debug(`User authenticated: ${user.email}`);
      } catch (error) {
        // Don't throw error here, let the guard handle it
        this.logger.debug('Authentication failed in middleware:', error instanceof Error ? error.message : String(error));
      }

      next();
    } catch (error) {
      this.logger.error('Auth middleware error:', error);
      next();
    }
  }

  private isPublicRoute(path: string): boolean {
    if (!path) return false;

    const publicRoutes = [
      '/health',
      '/api/health',
      '/api/docs',
      '/api/v1/health',
      '/api/v1/docs',
      // Add other public routes as needed
    ];

    return publicRoutes.some(route => path.startsWith(route));
  }
}
