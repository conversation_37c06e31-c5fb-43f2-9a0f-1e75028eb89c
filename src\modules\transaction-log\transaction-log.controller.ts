import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { TransactionLogService } from './transaction-log.service';
import { QueryTransactionLogDto } from './dto/query-transaction-log.dto';
import { Param, NotFoundException } from '@nestjs/common';
import { Public } from '../auth/src/auth.guard';

@ApiTags('Transaction Logs')
@Controller('transaction-logs')
export class TransactionLogController {
	constructor(private readonly transactionLogService: TransactionLogService) {}

	@Get()
	@ApiOperation({ summary: 'Get all transaction logs' })
	@ApiQuery({ name: 'query', type: QueryTransactionLogDto, required: false, description: 'Query parameters for filtering' })
	@ApiResponse({ status: 200, description: 'Transaction logs retrieved successfully' })
	async findAll(@Query() query: QueryTransactionLogDto) {
		return this.transactionLogService.findAll(query);
	}

		// GET /transaction-logs/:id - fetch a single log by ID
		@Get(':id')
		@ApiOperation({ summary: 'Get a transaction log by ID' })
		@ApiParam({ name: 'id', description: 'Transaction log ID' })
		@ApiResponse({ status: 200, description: 'Transaction log retrieved successfully' })
		@ApiResponse({ status: 404, description: 'Transaction log not found' })
		async findOne(@Param('id') id: string) {
			const log = await this.transactionLogService.findOne(id);
			if (!log) {
				throw new NotFoundException('Transaction log not found');
			}
			return log;
		}

		// GET /transaction-logs/by-merchant/:merchantId - fetch all logs for a merchant
		@Get('by-merchant/:merchantId')
		@ApiOperation({ summary: 'Get all transaction logs for a merchant' })
		@ApiParam({ name: 'merchantId', description: 'Merchant ID' })
		@ApiQuery({ name: 'query', type: QueryTransactionLogDto, required: false, description: 'Query parameters for filtering' })
		@ApiResponse({ status: 200, description: 'Transaction logs retrieved successfully' })
		async findByMerchant(@Param('merchantId') merchantId: string, @Query() query: QueryTransactionLogDto) {
			return this.transactionLogService.findByMerchant(merchantId, query);
		}
}
