import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { TransactionLogService } from '../transaction-log/transaction-log.service';
import { BaseSoftDeleteService } from '../../common/base-soft-delete.service';

@Injectable()
export class CategoryService extends BaseSoftDeleteService {
  constructor(
    protected readonly prisma: PrismaService,
    private readonly transactionLogService: TransactionLogService,
  ) {
    super(prisma);
  }

  protected getModelName(): string {
    return 'Category';
  }

  protected getModel() {
    return this.prisma.category;
  }

  async findAllByMerchant(merchantId: string) {
    return this.findAllActive(
      { merchantId },
      { subCategories: { where: { deletedAt: null } } }
    );
  }

  async create(dto: CreateCategoryDto) {
    try {
      const { subCategories, ...categoryData } = dto;
      const created = await this.prisma.category.create({
        data: {
          ...categoryData,
          subCategories: subCategories && subCategories.length > 0
            ? { create: subCategories.map(name => ({ name })) }
            : undefined,
        },
        include: { subCategories: true },
      });
      // Audit log - temporarily disabled for debugging
      // await this.transactionLogService.createLog({
      //   action: 'CREATE',
      //   entity: 'Category',
      //   entityId: created.id,
      //   userId: dto.merchantId,
      //   newValue: created,
      // });
      return created;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002' &&
        (error as any).meta?.target?.includes('name')
      ) {
        throw new BadRequestException('Category name already exists.');
      }
      throw error;
    }
  }

  async findOne(id: string, merchantId: string) {
    try {
      const category = await this.prisma.category.findUnique({
        where: { id },
        include: { subCategories: true },
      });
      if (!category) throw new NotFoundException('Category not found');
      if (category.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');
      return category;
    } catch (error) {
      throw new BadRequestException(
        (error instanceof Error ? error.message : 'Failed to fetch category')
      );
    }
  }

  async update(id: string, merchantId: string, dto: UpdateCategoryDto) {
    try {
      const before = await this.prisma.category.findUnique({
        where: { id },
        include: { subCategories: true },
      });
      if (!before) throw new NotFoundException('Category not found');
      if (before.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      const { subCategories, ...categoryData } = dto;
      let subCategoryOps = {};
      if (subCategories) {
        // Remove all existing subcategories and add new ones (simple replace strategy)
        subCategoryOps = {
          subCategories: {
            deleteMany: {},
            create: subCategories.map(name => ({ name })),
          },
        };
      }

      const updated = await this.prisma.category.update({
        where: { id },
        data: {
          ...categoryData,
          ...subCategoryOps,
        },
        include: { subCategories: true },
      });
      // Audit log
      await this.transactionLogService.createLog({
        action: 'UPDATE',
        entity: 'Category',
        entityId: updated.id,
        userId: merchantId,
        oldValue: before,
        newValue: updated,
      });
      return updated;
    } catch (error) {
      if (
        typeof error === 'object' &&
        error !== null &&
        'code' in error &&
        (error as any).code === 'P2002' &&
        (error as any).meta?.target?.includes('name')
      ) {
        throw new BadRequestException('Category name already exists.');
      }
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to update category'
      );
    }
  }

  async remove(id: string, merchantId: string) {
    try {
      const before = await this.prisma.category.findUnique({
        where: { id },
        include: { subCategories: true }
      });
      if (!before) throw new NotFoundException('Category not found');
      if (before.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      // Soft delete all subcategories for this category first
      await this.prisma.subCategory.updateMany({
        where: { categoryId: id, deletedAt: null },
        data: { deletedAt: new Date() }
      });

      // Soft delete the category
      const deleted = await this.softDelete(id, merchantId);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'SOFT_DELETE',
        entity: 'Category',
        entityId: deleted.id,
        userId: merchantId,
        oldValue: before,
      });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to delete category'
      );
    }
  }

  // New methods for soft delete management
  async findDeletedByMerchant(merchantId: string) {
    return this.findAllDeleted(
      { merchantId },
      { subCategories: true }
    );
  }

  async restoreCategory(id: string, merchantId: string) {
    try {
      const category = await this.prisma.category.findUnique({ where: { id } });
      if (!category) throw new NotFoundException('Category not found');
      if (category.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      const restored = await this.restore(id);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'RESTORE',
        entity: 'Category',
        entityId: restored.id,
        userId: merchantId,
        newValue: restored,
      });
      return restored;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to restore category'
      );
    }
  }

  async permanentDeleteCategory(id: string, merchantId: string) {
    try {
      const category = await this.prisma.category.findUnique({ where: { id } });
      if (!category) throw new NotFoundException('Category not found');
      if (category.merchantId !== merchantId) throw new BadRequestException('Merchant mismatch');

      // Permanently delete all subcategories first
      await this.prisma.subCategory.deleteMany({ where: { categoryId: id } });

      const deleted = await this.permanentDelete(id);

      // Audit log
      await this.transactionLogService.createLog({
        action: 'PERMANENT_DELETE',
        entity: 'Category',
        entityId: deleted.id,
        userId: merchantId,
        oldValue: category,
      });
      return deleted;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : 'Failed to permanently delete category'
      );
    }
  }
}