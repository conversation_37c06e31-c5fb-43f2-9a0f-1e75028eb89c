import { ObjectType, Field, ID, Int, registerEnumType } from '@nestjs/graphql';
import { ProductStatus, SaleType } from '@prisma/client';

// Register enums for GraphQL
registerEnumType(ProductStatus, {
  name: 'ProductStatus',
});

registerEnumType(SaleType, {
  name: 'SaleType',
});

@ObjectType()
export class ProductEntity {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field(() => Int, { nullable: true })
  price?: number;

  @Field()
  sku: string;

  @Field(() => Int)
  count: number;

  @Field()
  categoryId: string;

  @Field({ nullable: true })
  subCategoryId?: string;

  @Field()
  merchantId: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => ProductStatus)
  status: ProductStatus;

  @Field(() => SaleType)
  saleType: SaleType;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  deletedAt?: Date;
}
