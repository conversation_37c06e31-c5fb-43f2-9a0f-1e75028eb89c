import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreateMerchantDto } from './dto/merchant.dto';

@Injectable()
export class MerchantService {
  constructor(private readonly prisma: PrismaService) {}

  async exists(merchantId: string): Promise<boolean> {
    const merchant = await this.prisma.merchant.findUnique({ where: { id: merchantId } });
    return !!merchant;
  }

  async create(dto: CreateMerchantDto) {
    return this.prisma.merchant.create({
      data: dto,
    });
  }

  async findAll() {
    return this.prisma.merchant.findMany();
  }

  async findOne(id: string) {
    return this.prisma.merchant.findUnique({
      where: { id },
      include: {
        products: true,
        categories: true,
        discounts: true,
      },
    });
  }

  async update(id: string, dto: Partial<CreateMerchantDto>) {
    return this.prisma.merchant.update({
      where: { id },
      data: dto,
    });
  }

  async remove(id: string) {
    return this.prisma.merchant.delete({
      where: { id },
    });
  }
}
