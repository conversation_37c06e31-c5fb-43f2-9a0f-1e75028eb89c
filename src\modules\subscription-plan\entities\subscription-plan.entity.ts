import { ObjectType, Field, ID, Int, registerEnumType } from '@nestjs/graphql';
import { SubscriptionPlanStatus } from '@prisma/client';
import { ProductEntity } from '../../product/entities/product.entity';

// Register enum for GraphQL
registerEnumType(SubscriptionPlanStatus, {
  name: 'SubscriptionPlanStatus',
});

@ObjectType()
export class SubscriptionPlanEntity {
  @Field(() => ID)
  id: string;

  @Field()
  productId: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => SubscriptionPlanStatus)
  status: SubscriptionPlanStatus;

  @Field()
  recurringMode: string;

  @Field(() => Int)
  recurringInterval: number;

  @Field(() => Int)
  recurringFrequency: number;

  @Field(() => Int)
  recurringTotalCycles: number;

  @Field(() => Int, { nullable: true })
  recurringTrialDays?: number;

  @Field(() => Int, { nullable: true })
  recurringSetupFee?: number;

  @Field()
  recurringRefundable: boolean;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  // Relations
  @Field(() => ProductEntity)
  product: ProductEntity;
}
