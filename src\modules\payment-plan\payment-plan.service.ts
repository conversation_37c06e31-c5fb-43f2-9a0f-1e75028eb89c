import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CreatePaymentPlanDto, UpdatePaymentPlanDto } from './dto/payment-plan.dto';

@Injectable()
export class PaymentPlanService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreatePaymentPlanDto) {
    try {
      return await this.prisma.paymentPlan.create({ data: dto });
    } catch (error) {
      throw new BadRequestException(error instanceof Error ? error.message : 'Failed to create payment plan');
    }
  }

  async findByProduct(productId: string) {
    return this.prisma.paymentPlan.findUnique({ where: { productId } });
  }

  async update(productId: string, dto: UpdatePaymentPlanDto) {
    try {
      return await this.prisma.paymentPlan.update({ where: { productId }, data: dto });
    } catch (error) {
      throw new BadRequestException(error instanceof Error ? error.message : 'Failed to update payment plan');
    }
  }

  async remove(productId: string) {
    try {
      return await this.prisma.paymentPlan.delete({ where: { productId } });
    } catch (error) {
      throw new NotFoundException('Payment plan not found');
    }
  }
}
