
import { Module, Global } from '@nestjs/common';
import { TransactionLogService } from './transaction-log.service';
import { TransactionLogController } from './transaction-log.controller';

import { PrismaModule } from '../../prisma/prisma.module';

@Global()
@Module({
	imports: [PrismaModule],
	controllers: [TransactionLogController],
	providers: [TransactionLogService],
	exports: [TransactionLogService],
})
export class TransactionLogModule {}
