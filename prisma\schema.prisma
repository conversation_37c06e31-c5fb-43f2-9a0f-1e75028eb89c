generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Merchant {
  id          String     @id @default(cuid())
  name        String
  deletedAt   DateTime?
  products    Product[]
  categories  Category[]
  discounts   Discount[]
}

model Category {
  id            String       @id @default(cuid())
  name          String       @unique
  merchantId    String
  deletedAt     DateTime?
  subCategories SubCategory[]
  products      Product[]
  merchant      Merchant     @relation(fields: [merchantId], references: [id])
}

model SubCategory {
  id          String     @id @default(cuid())
  name        String
  categoryId  String
  deletedAt   DateTime?
  category    Category  @relation(fields: [categoryId], references: [id])
  products    Product[]
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  PENDING
  DISCONTINUED
}
enum SaleType {
  CASH
  SUBSCRIPTION
  INSTALLMENT
}

model Product {
  id              String    @id @default(cuid())
  name            String
  price           Int?
  sku             String
  count           Int       @default(0)
  categoryId      String
  subCategoryId   String?
  brand           String?
  itemWeight      String?
  length          String?
  width           String?
  description     String?
  isInStore       Boolean   @default(false)
  isOnline        Boolean   @default(false)
  productImages   Json?
  createdAt       DateTime  @default(now())
  deletedAt       DateTime?
  merchantId      String
  productStatus   ProductStatus? @default(INACTIVE)        
  subscriptionPlans SubscriptionPlan[]
  paymentPlans      PaymentPlan[]
  saleType         SaleType   @default(CASH)
  variants         Variant[]
  category         Category    @relation(fields: [categoryId], references: [id])
  subCategory      SubCategory? @relation(fields: [subCategoryId], references: [id])
  merchant         Merchant    @relation(fields: [merchantId], references: [id])
  discounts        Discount[]

  @@unique([sku, merchantId])
}

model Variant {
  id         String   @id @default(cuid())
  productId  String
  name       String
  sku        String
  price      Int?
  attributes Json?
  deletedAt  DateTime?

  product    Product  @relation(fields: [productId], references: [id])

  @@unique([productId, sku])
}

model SubscriptionPlan {
  id                   String   @id @default(cuid())
  productId            String   @unique
  name                 String
  description          String?
  status               SubscriptionPlanStatus @default(ACTIVE)
  recurringMode        String
  recurringInterval    Int
  recurringFrequency   Int
  recurringTotalCycles Int
  recurringTrialDays   Int?
  recurringSetupFee    Int?
  recurringRefundable  Boolean  @default(false)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt
  deletedAt            DateTime?

  product              Product  @relation(fields: [productId], references: [id])
}

model PaymentPlan {
  id                 String   @id @default(cuid())
  productId          String   @unique
  name               String
  description        String?
  status             PaymentPlanStatus @default(ACTIVE)
  frequency          PaymentPlanFrequency
  totalAmount        Int
  installmentAmount  Int
  totalInstallments  Int
  balance            Int      @default(0)
  gracePeriodDays    Int?
  lateFee            Int?
  refundable         Boolean  @default(false)
  schedule           Json?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  deletedAt          DateTime?

  product            Product  @relation(fields: [productId], references: [id])
}
enum SubscriptionPlanStatus {
  ACTIVE
  INACTIVE
}

enum PaymentPlanStatus {
  ACTIVE
  INACTIVE
}

enum PaymentPlanFrequency {
  WEEKLY
  MONTHLY
  YEARLY
}

model Discount {
  id            String    @id @default(cuid())
  name          String
  description   String?
  type          DiscountType
  discount      Int?
  maxDiscount   Int?
  validFrom     DateTime?
  validTo       DateTime?
  maxClaims     Int?
  claims        Int?
  status        DiscountStatus
  scope         String    // product | purchase
  createdAt     DateTime  @default(now())
  deletedAt     DateTime?
  merchantId    String
  merchant      Merchant  @relation(fields: [merchantId], references: [id])
  products      Product[]

  @@unique([name, merchantId])
}

enum DiscountStatus {
  ACTIVE
  INACTIVE
  EXPIRED
  SCHEDULED
}

enum DiscountType {
  FLAT
  PERCENTAGE
}

model TransactionLog {
  id        String   @id @default(cuid())
  action    String
  entity    String
  entityId  String
  userId    String?
  oldValue  Json?
  newValue  Json?
  createdAt DateTime @default(now())
  deletedAt DateTime?
}
