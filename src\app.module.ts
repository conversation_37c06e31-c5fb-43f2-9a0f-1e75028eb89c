import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { ConfigModule } from '@nestjs/config';
import { join } from 'path';
import { APP_GUARD } from '@nestjs/core';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProductModule } from './modules/product/product.module';
import { MerchantModule } from './modules/merchant/merchant.module';
import { SubscriptionPlanModule } from './modules/subscription-plan/subscription-plan.module';
import { PaymentPlanModule } from './modules/payment-plan/payment-plan.module';
import { DiscountModule } from './modules/discount/discount.module';
import { PrismaModule } from './prisma/prisma.module';
import { TransactionLogModule } from './modules/transaction-log/transaction-log.module';
import { CategoryModule } from './modules/category/category.module';
import { SubCategoryModule } from './modules/subcategory/subcategory.module';
import { VariantModule } from './variant/variant.module';
import { AuthModule } from './modules/auth/src/auth.module';
import { AuthGuard } from './modules/auth/src/auth.guard';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      playground: {
        settings: {
          'request.credentials': 'include',
        },
      },
      introspection: true,
      context: ({ req, res }: { req: any; res: any }) => ({ req, res }),
      cors: {
        origin: true,
        credentials: true,
      },
      formatError: (error: any) => {
        console.error('GraphQL Error:', error);
        return {
          message: error.message,
          code: error.extensions?.code,
          path: error.path,
        };
      },
    }),
    PrismaModule,
    ProductModule,
    CategoryModule,
    SubCategoryModule,
    VariantModule,
    DiscountModule,
    SubscriptionPlanModule,
    TransactionLogModule,
    MerchantModule,
    PaymentPlanModule,
    AuthModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class AppModule {}
