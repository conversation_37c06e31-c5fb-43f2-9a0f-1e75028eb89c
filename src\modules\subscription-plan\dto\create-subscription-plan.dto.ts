import { IsString, IsOptional, IsInt, IsEnum, IsBoolean } from 'class-validator';

export enum SubscriptionPlanStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export class CreateSubscriptionPlanDto {
  @IsString()
  productId!: string;

  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsEnum(SubscriptionPlanStatus)
  status?: SubscriptionPlanStatus;

  @IsString()
  recurringMode!: string;

  @IsInt()
  recurringInterval!: number;

  @IsInt()
  recurringFrequency!: number;

  @IsInt()
  recurringTotalCycles!: number;

  @IsOptional()
  @IsInt()
  recurringTrialDays?: number;

  @IsOptional()
  @IsInt()
  recurringSetupFee?: number;

  @IsOptional()
  @IsBoolean()
  recurringRefundable?: boolean;
}
