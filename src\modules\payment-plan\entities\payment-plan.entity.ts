import { ObjectType, Field, ID, Int, registerEnumType } from '@nestjs/graphql';
import { PaymentPlanStatus, PaymentPlanFrequency } from '@prisma/client';
import { ProductEntity } from '../../product/entities/product.entity';

// Register enums for GraphQL
registerEnumType(PaymentPlanStatus, {
  name: 'PaymentPlanStatus',
});

registerEnumType(PaymentPlanFrequency, {
  name: 'PaymentPlanFrequency',
});

@ObjectType()
export class PaymentPlanEntity {
  @Field(() => ID)
  id: string;

  @Field()
  productId: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => PaymentPlanStatus)
  status: PaymentPlanStatus;

  @Field(() => PaymentPlanFrequency)
  frequency: PaymentPlanFrequency;

  @Field(() => Int)
  totalAmount: number;

  @Field(() => Int)
  installmentAmount: number;

  @Field(() => Int)
  totalInstallments: number;

  @Field(() => Int)
  balance: number;

  @Field(() => Int, { nullable: true })
  gracePeriodDays?: number;

  @Field(() => Int, { nullable: true })
  lateFee?: number;

  @Field()
  refundable: boolean;

  @Field({ nullable: true })
  schedule?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  // Relations
  @Field(() => ProductEntity)
  product: ProductEntity;
}
