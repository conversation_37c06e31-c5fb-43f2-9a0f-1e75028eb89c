import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { QueryTransactionLogDto } from './dto/query-transaction-log.dto';

@Injectable()
export class TransactionLogService {
	constructor(private readonly prisma: PrismaService) {}

	async createLog(data: {
		action: string;
		entity: string;
		entityId: string;
		userId?: string;
		oldValue?: any;
		newValue?: any;
	}) {
		return this.prisma.transactionLog.create({
			data: {
				action: data.action,
				entity: data.entity,
				entityId: data.entityId,
				userId: data.userId,
				oldValue: data.oldValue,
				newValue: data.newValue,
			},
		});
	}

	async findAll(query: QueryTransactionLogDto) {
		const { entity, entityId, userId, action, limit = 20, offset = 0 } = query;
		return this.prisma.transactionLog.findMany({
			where: {
				...(entity && { entity }),
				...(entityId && { entityId }),
				...(userId && { userId }),
				...(action && { action }),
			},
			orderBy: { createdAt: 'desc' },
			skip: offset,
			take: limit,
		});
	}

		// Find a single transaction log by its ID
		async findOne(id: string) {
			return this.prisma.transactionLog.findUnique({
				where: { id },
			});
		}

		// Find all transaction logs for a specific merchant (assuming userId is merchantId)
		async findByMerchant(merchantId: string, query: QueryTransactionLogDto) {
			const { entity, entityId, action, limit = 20, offset = 0 } = query;
			return this.prisma.transactionLog.findMany({
				where: {
					userId: merchantId,
					...(entity && { entity }),
					...(entityId && { entityId }),
					...(action && { action }),
				},
				orderBy: { createdAt: 'desc' },
				skip: offset,
				take: Math.min(limit, 100), // Cap at 100 records
			});
		}

		// Cleanup old transaction logs (older than 90 days)
		async cleanupOldLogs() {
			const ninetyDaysAgo = new Date();
			ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

			const deleted = await this.prisma.transactionLog.deleteMany({
				where: {
					createdAt: {
						lt: ninetyDaysAgo,
					},
				},
			});

			console.log(`🧹 Cleaned up ${deleted.count} old transaction logs`);
			return deleted;
		}
}
