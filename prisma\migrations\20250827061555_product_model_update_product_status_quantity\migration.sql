/*
  Warnings:

  - The `productStatus` column on the `Product` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "ProductStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'DISCONTINUED');

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "count" INTEGER NOT NULL DEFAULT 0,
DROP COLUMN "productStatus",
ADD COLUMN     "productStatus" "ProductStatus" DEFAULT 'INACTIVE';
