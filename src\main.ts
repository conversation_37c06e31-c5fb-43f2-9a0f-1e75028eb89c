import 'reflect-metadata';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
const cookie = require('@fastify/cookie');

async function bootstrap() {
  // For Docker containers, always bind to 0.0.0.0 to accept external connections
  // Check if we're running in Docker by looking for container-specific environment variables
  const isDocker = process.env.HOSTNAME && process.env.HOSTNAME.length === 12 || // Docker container hostname
                   process.env.DOCKER_ENV === 'true' ||
                   process.env.NODE_ENV === 'production' ||
                   process.env.DATABASE_URL?.includes('product-backend-db'); // Docker service name

  const isDevOrTest = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
  const sourceIp = isDocker ? '0.0.0.0' : (process.env.SOURCE_IP || '127.0.0.1');
  
  const fastifyAdapter = new FastifyAdapter({
    logger: process.env.NODE_ENV !== 'production',
    maxParamLength: 100,
    bodyLimit: 10485760, // 10MB
  });

  await (fastifyAdapter.getInstance() as any).register(cookie, {
    secret: process.env.COOKIE_SECRET || 'default-secret-key-for-development', // for signed cookies
    parseOptions: {
      decode: decodeURIComponent, // Properly decode URL-encoded cookies
      maxAge: 86400000, // 24 hours
      httpOnly: false, // Allow client-side access if needed
      secure: false, // Set to true in production with HTTPS
      sameSite: 'lax' // CSRF protection
    }
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
  );

  // Add global validation pipe
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  const listen_port = process.env.LISTENING_PORT || process.env.PORT || 3070;

  // IP Restriction Middleware - Only allow requests from specified SOURCE_IP
  app.use((req: any, res: any, next: any) => {
    const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || req.headers['x-real-ip'];
    const allowedIP = process.env.SOURCE_IP || '127.0.0.1';

    console.log(`🌐 Request from IP: ${clientIP}, Allowed IP: ${allowedIP}`);

    // In development, only allow requests from the specified SOURCE_IP (but allow localhost for Docker)
    if (isDevOrTest && !isDocker && clientIP !== allowedIP && clientIP !== '127.0.0.1' && clientIP !== '::1') {
      console.warn(`🚫 Blocked request from unauthorized IP: ${clientIP}`);
      return res.status(403).send({ error: 'Access denied from this IP address' });
    }

    next();
  });

  // Define allowed origins based on environment
  const allowedOrigins = isDevOrTest
    ? [
        'http://localhost:3041',  // Local frontend development
        'http://localhost:3042',  // Local admin development
        'http://ng-support-fe-local.dev.dev1.ngnair.com:3041',  // Frontend service
        'http://ng-support-admin-local.dev.dev1.ngnair.com:3042',  // Admin service
        'https://ng-auth-dev.dev1.ngnair.com'  // Auth service
      ]
    : [
        process.env.FRONTEND_URL,
        process.env.ADMIN_URL,
        process.env.AUTH_URL,
        process.env.OTHER_URL,
      ].filter((origin): origin is string => Boolean(origin));

  app.enableCors({
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  });

  // Register multipart support for file uploads
  await app.register(require('@fastify/multipart'), {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB max per file
      files: 10, // max 10 files per request
    },
  });

  app.setGlobalPrefix('api/v1');

  const config = new DocumentBuilder()
    .setTitle('Product Service')
    .setDescription('API for managing Product Service')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        in: 'header',
      },
    )
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config);
  if (process.env.NODE_ENV === 'production') {
    // Disable console logs in production
    console.log = () => {};
    console.warn = () => {};
    console.info = () => {};
    console.debug = () => {};
  }

  SwaggerModule.setup('api', app, documentFactory);

  if (process.env.NODE_ENV !== 'production') {
    console.log(`🚀 Server starting on ${sourceIp}:${listen_port}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔒 IP Restriction: Only ${process.env.SOURCE_IP || '127.0.0.1'} allowed`);
    console.log(`🌐 CORS enabled for allowed origins`);
    console.log(`📚 Swagger docs available at: /api`);
  }

  await app.listen(listen_port, sourceIp);
}
bootstrap();
